import { Variants } from 'framer-motion'

// 🌱 HSSL Natural Green Color System
// Modern, clean, and cohesive theme centered around sustainability and wellness
export const colorTheme = {
  // 🎯 Primary Actions - Deep Green (Accent Layer 1) for main CTAs
  primary: {
    gradient: 'linear-gradient(135deg, var(--primary-600), var(--secondary-500))',
    gradientHover: 'linear-gradient(135deg, var(--primary-700), var(--secondary-600))',
    bg: 'var(--primary-600)', /* Deep Green - Primary actions */
    bgHover: 'var(--primary-700)', /* Deep Forest Green */
    bgActive: 'var(--primary-800)', /* Deep Forest Green */
    text: 'var(--primary-600)', /* Deep Green */
    textHover: 'var(--primary-700)', /* Deep Forest Green */
    light: 'var(--accent-50)', /* <PERSON><PERSON>t (Accent Layer 4) */
    lightHover: 'var(--accent-100)', /* Light Mint Green (Accent Layer 3) */
    border: 'var(--accent-200)', /* Light Mint Green */
    borderHover: 'var(--accent-300)', /* Mid Green */
    ring: 'var(--primary-500)', /* Deep Green */
    shadow: 'rgba(42, 94, 68, 0.25)'
  },

  // 🌿 Secondary Actions - Mid Green (Accent Layer 2) for supporting elements
  secondary: {
    gradient: 'linear-gradient(135deg, var(--secondary-500), var(--accent-500))',
    gradientHover: 'linear-gradient(135deg, var(--secondary-600), var(--accent-600))',
    bg: 'var(--secondary-500)', /* Mid Green - Secondary buttons, highlights */
    bgHover: 'var(--secondary-600)', /* Mid Green */
    bgActive: 'var(--secondary-700)', /* Deep Green */
    text: 'var(--secondary-500)', /* Mid Green */
    textHover: 'var(--secondary-600)', /* Mid Green */
    light: 'var(--accent-100)', /* Light Mint Green (Accent Layer 3) */
    lightHover: 'var(--accent-200)', /* Light Mint Green */
    border: 'var(--accent-200)', /* Light Mint Green */
    borderHover: 'var(--accent-300)', /* Mid Green */
    ring: 'var(--secondary-500)', /* Mid Green */
    shadow: 'rgba(99, 154, 115, 0.25)'
  },

  // ✨ Success States - Light Mint Green for positive feedback
  success: {
    gradient: 'linear-gradient(135deg, var(--success-500), var(--success-600))',
    gradientHover: 'linear-gradient(135deg, var(--success-600), var(--success-700))',
    bg: 'var(--success-500)', /* Mid Green */
    bgHover: 'var(--success-600)', /* Deep Green */
    text: 'var(--success-700)', /* Deep Green */
    light: 'var(--success-50)', /* Pale Green Tint */
    border: 'var(--success-200)', /* Light Mint Green */
    ring: 'var(--success-500)', /* Mid Green */
    shadow: 'rgba(99, 154, 115, 0.2)'
  },

  // ⚠️ Warning States - Earthy amber for alerts (keeping original)
  warning: {
    gradient: 'linear-gradient(135deg, var(--warning-400), var(--warning-500))',
    gradientHover: 'linear-gradient(135deg, var(--warning-500), var(--warning-600))',
    bg: 'var(--warning-500)',
    bgHover: 'var(--warning-600)',
    text: 'var(--warning-700)',
    light: 'var(--warning-50)',
    border: 'var(--warning-200)',
    ring: 'var(--warning-500)',
    shadow: 'rgba(245, 158, 11, 0.2)'
  },

  // 🚨 Error States - Muted rust tone (keeping original)
  error: {
    gradient: 'linear-gradient(135deg, var(--error-400), var(--error-500))',
    gradientHover: 'linear-gradient(135deg, var(--error-500), var(--error-600))',
    bg: 'var(--error-500)',
    bgHover: 'var(--error-600)',
    text: 'var(--error-700)',
    light: 'var(--error-50)',
    border: 'var(--error-200)',
    ring: 'var(--error-500)',
    shadow: 'rgba(239, 68, 68, 0.2)'
  },

  // 🌿 Neutral/Tertiary - Cream and green tones for backgrounds
  neutral: {
    gradient: 'linear-gradient(135deg, var(--neutral-50), var(--neutral-100))',
    gradientHover: 'linear-gradient(135deg, var(--neutral-100), var(--neutral-200))',
    bg: 'var(--neutral-100)', /* Pale Green Tint */
    bgHover: 'var(--neutral-200)', /* Light Mint Green */
    text: 'var(--neutral-500)', /* Deep Forest Green */
    textHover: 'var(--neutral-600)', /* Deep Forest Green */
    light: 'var(--neutral-50)', /* Cream */
    lightHover: 'var(--neutral-100)', /* Pale Green Tint */
    border: 'var(--neutral-200)', /* Light Mint Green */
    borderHover: 'var(--neutral-300)', /* Mid Green */
    ring: 'var(--neutral-500)', /* Deep Forest Green */
    shadow: 'rgba(33, 77, 58, 0.1)'
  },

  // 🌱 Accent/Contrast - Light Mint Green for emphasis
  accent: {
    gradient: 'linear-gradient(135deg, var(--accent-600), var(--accent-700))',
    gradientHover: 'linear-gradient(135deg, var(--accent-700), var(--accent-800))',
    bg: 'var(--accent-500)', /* Deep Green */
    bgHover: 'var(--accent-600)', /* Deep Green */
    text: 'var(--accent-700)', /* Deep Forest Green */
    textHover: 'var(--accent-800)', /* Deep Forest Green */
    light: 'var(--accent-50)', /* Pale Green Tint */
    lightHover: 'var(--accent-100)', /* Light Mint Green */
    border: 'var(--accent-300)', /* Mid Green */
    borderHover: 'var(--accent-400)', /* Mid Green */
    ring: 'var(--accent-600)', /* Deep Green */
    shadow: 'rgba(174, 206, 169, 0.25)'
  }
}

// 🌈 Recommended Gradient System for Different Use Cases
export const gradientSystem = {
  // Hero sections - Bold, eye-catching gradients
  hero: {
    primary: 'linear-gradient(135deg, var(--primary-600), var(--secondary-500), var(--accent-500))',
    secondary: 'linear-gradient(135deg, var(--accent-200), var(--accent-100), var(--accent-50))',
    accent: 'linear-gradient(135deg, var(--accent-600), var(--primary-600), var(--secondary-700))'
  },

  // Card backgrounds and overlays - Subtle, elegant
  cards: {
    primary: 'linear-gradient(135deg, var(--accent-50), var(--background), var(--accent-50))',
    secondary: 'linear-gradient(135deg, var(--accent-100), var(--accent-50), var(--accent-100))',
    accent: 'linear-gradient(135deg, var(--background), var(--accent-50), var(--background))'
  },

  // Buttons and interactive elements - Vibrant, engaging
  interactive: {
    primary: 'linear-gradient(135deg, var(--primary-600), var(--secondary-500))',
    secondary: 'linear-gradient(135deg, var(--secondary-500), var(--accent-500))',
    accent: 'linear-gradient(135deg, var(--accent-600), var(--primary-700))',
    hover: {
      primary: 'linear-gradient(135deg, var(--primary-700), var(--secondary-600))',
      secondary: 'linear-gradient(135deg, var(--secondary-600), var(--accent-600))',
      accent: 'linear-gradient(135deg, var(--accent-700), var(--primary-800))'
    }
  },

  // Section backgrounds - Organic, flowing transitions
  sections: {
    light: 'linear-gradient(135deg, var(--accent-50), var(--background), var(--accent-50))',
    medium: 'linear-gradient(135deg, var(--accent-100), var(--accent-50), var(--accent-100))',
    dark: 'linear-gradient(135deg, var(--primary-800), var(--secondary-800), var(--accent-900))'
  }
}

// 🎨 Section Background Alternation Patterns
export const sectionPatterns = {
  // Standard alternating pattern
  standard: [
    'var(--background)', /* Cream */
    'linear-gradient(to bottom right, var(--accent-50), var(--background), var(--accent-50))',
    'var(--accent-50)', /* Pale Green Tint */
    'linear-gradient(to bottom right, var(--accent-100), var(--accent-50), var(--accent-100))'
  ],

  // Subtle gradient pattern
  subtle: [
    'linear-gradient(to bottom right, var(--background), var(--accent-50), var(--background))',
    'linear-gradient(to bottom right, var(--accent-50), var(--background), var(--accent-50))',
    'linear-gradient(to bottom right, var(--accent-100), var(--accent-50), var(--background))',
    'linear-gradient(to bottom right, var(--background), var(--accent-100), var(--accent-50))'
  ],

  // Bold contrast pattern
  bold: [
    'var(--background)', /* Cream */
    'linear-gradient(to bottom right, var(--primary-600), var(--secondary-500))',
    'var(--accent-50)', /* Pale Green Tint */
    'linear-gradient(to bottom right, var(--secondary-600), var(--accent-700))'
  ]
}

// Enhanced animation variants
export const fadeInUp: Variants = {
  initial: { 
    opacity: 0, 
    y: 60,
    scale: 0.95
  },
  animate: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

export const fadeInDown: Variants = {
  initial: { 
    opacity: 0, 
    y: -60,
    scale: 0.95
  },
  animate: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

export const fadeInLeft: Variants = {
  initial: { 
    opacity: 0, 
    x: -60,
    scale: 0.95
  },
  animate: { 
    opacity: 1, 
    x: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

export const fadeInRight: Variants = {
  initial: { 
    opacity: 0, 
    x: 60,
    scale: 0.95
  },
  animate: { 
    opacity: 1, 
    x: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

export const scaleIn: Variants = {
  initial: { 
    opacity: 0, 
    scale: 0.8,
    rotate: -5
  },
  animate: { 
    opacity: 1, 
    scale: 1,
    rotate: 0,
    transition: {
      duration: 0.7,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

export const slideInUp: Variants = {
  initial: { 
    opacity: 0, 
    y: 100,
    scale: 0.9
  },
  animate: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.9,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

// Stagger container for multiple items
export const staggerContainer: Variants = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.2
    }
  }
}

export const staggerItem: Variants = {
  initial: { 
    opacity: 0, 
    y: 40,
    scale: 0.95
  },
  animate: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

// Floating animation for hero elements
export const floating: Variants = {
  initial: { y: 0 },
  animate: {
    y: [-10, 10, -10],
    transition: {
      duration: 6,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
}

// Pulse animation for attention-grabbing elements
export const pulse: Variants = {
  initial: { scale: 1 },
  animate: {
    scale: [1, 1.05, 1],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
}

// Hover animations
export const hoverScale = {
  whileHover: {
    scale: 1.05,
    y: -5,
    transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
  },
  whileTap: {
    scale: 0.98,
    transition: { duration: 0.1 }
  }
}

export const hoverRotate = {
  whileHover: {
    scale: 1.1,
    rotate: 5,
    transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
  },
  whileTap: {
    scale: 0.95,
    rotate: 0,
    transition: { duration: 0.1 }
  }
}

export const hoverGlow = {
  whileHover: { 
    scale: 1.02,
    boxShadow: "0 20px 40px rgba(34, 197, 94, 0.3)",
    transition: { duration: 0.3, ease: "easeOut" }
  }
}

// Text reveal animation
export const textReveal: Variants = {
  initial: { 
    opacity: 0,
    y: 50,
    skewY: 10
  },
  animate: { 
    opacity: 1,
    y: 0,
    skewY: 0,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

// Page transition
export const pageTransition: Variants = {
  initial: { 
    opacity: 0,
    scale: 0.98,
    y: 20
  },
  animate: { 
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  },
  exit: { 
    opacity: 0,
    scale: 0.98,
    y: -20,
    transition: {
      duration: 0.4,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

// Scroll-triggered animations
export const scrollReveal: Variants = {
  initial: { 
    opacity: 0, 
    y: 60,
    scale: 0.95
  },
  whileInView: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

export const scrollStagger: Variants = {
  initial: {},
  whileInView: {
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
}
