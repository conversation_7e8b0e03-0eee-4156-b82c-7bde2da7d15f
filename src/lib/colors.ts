// 🎨 HSSL Natural Green Color System Utilities
// Modern, clean, and cohesive color management for sustainability-focused design

import { colorTheme, gradientSystem, sectionPatterns } from './animations'

// 🌱 Natural Green Color Role Definitions
export const colorRoles = {
  // Primary brand colors - Natural Green Palette
  brand: {
    primary: 'var(--primary-600)', /* Deep Green (Accent Layer 1) */
    secondary: 'var(--secondary-500)', /* Mid Green (Accent Layer 2) */
    accent: 'var(--accent-500)' /* Light Mint Green (Accent Layer 3) */
  },

  // Semantic colors - Maintaining accessibility
  semantic: {
    success: 'var(--success-500)', /* Mid Green */
    warning: 'var(--warning-500)', /* Earthy Amber */
    error: 'var(--error-500)', /* Muted Red */
    info: 'var(--secondary-500)' /* Mid Green */
  },

  // UI element colors - Natural, calming palette
  ui: {
    background: 'var(--background)', /* Cream - Site-wide background */
    surface: 'var(--accent-50)', /* Pale Green Tint (Accent Layer 4) */
    border: 'var(--accent-200)', /* Light Mint Green */
    text: {
      primary: 'var(--foreground)', /* Deep Forest Green - Primary text */
      secondary: 'var(--secondary-500)', /* Mid Green */
      muted: 'var(--accent-300)' /* Mid Green - Muted text */
    }
  }
}

// 🎯 Button Color Variants (CSS Variable-based)
export const buttonVariants = {
  // Primary action buttons
  primary: {
    base: {
      backgroundColor: 'var(--primary-600)', /* Deep Green */
      color: 'var(--primary-foreground)', /* Cream */
      borderColor: 'var(--primary-600)'
    },
    hover: {
      backgroundColor: 'var(--primary-700)', /* Deep Forest Green */
      borderColor: 'var(--primary-700)'
    },
    active: {
      backgroundColor: 'var(--primary-800)' /* Deep Forest Green */
    },
    focus: {
      boxShadow: '0 0 0 2px var(--primary-500), 0 0 0 4px rgba(42, 94, 68, 0.2)'
    },
    disabled: {
      backgroundColor: 'var(--primary-200)', /* Light Mint Green */
      borderColor: 'var(--primary-200)',
      opacity: 0.6
    }
  },

  // Secondary action buttons
  secondary: {
    base: {
      backgroundColor: 'var(--accent-100)', /* Light Mint Green */
      color: 'var(--primary-700)', /* Deep Forest Green */
      borderColor: 'var(--accent-200)'
    },
    hover: {
      backgroundColor: 'var(--accent-200)', /* Light Mint Green */
      borderColor: 'var(--accent-300)'
    },
    active: {
      backgroundColor: 'var(--accent-300)' /* Mid Green */
    },
    focus: {
      boxShadow: '0 0 0 2px var(--secondary-500), 0 0 0 4px rgba(99, 154, 115, 0.2)'
    },
    disabled: {
      backgroundColor: 'var(--accent-50)', /* Pale Green Tint */
      color: 'var(--accent-400)',
      opacity: 0.6
    }
  },

  // Outline buttons
  outline: {
    base: {
      backgroundColor: 'transparent',
      color: 'var(--primary-600)', /* Deep Green */
      borderColor: 'var(--primary-600)'
    },
    hover: {
      backgroundColor: 'var(--accent-50)', /* Pale Green Tint */
      borderColor: 'var(--primary-700)',
      color: 'var(--primary-700)' /* Deep Forest Green */
    },
    active: {
      backgroundColor: 'var(--accent-100)' /* Light Mint Green */
    },
    focus: {
      boxShadow: '0 0 0 2px var(--primary-500), 0 0 0 4px rgba(42, 94, 68, 0.2)'
    },
    disabled: {
      color: 'var(--primary-300)',
      borderColor: 'var(--primary-300)',
      opacity: 0.6
    }
  },

  // Ghost buttons
  ghost: {
    base: {
      backgroundColor: 'transparent',
      color: 'var(--primary-600)', /* Deep Green */
      borderColor: 'transparent'
    },
    hover: {
      backgroundColor: 'var(--accent-50)', /* Pale Green Tint */
      color: 'var(--primary-700)' /* Deep Forest Green */
    },
    active: {
      backgroundColor: 'var(--accent-100)' /* Light Mint Green */
    },
    focus: {
      boxShadow: '0 0 0 2px var(--primary-500), 0 0 0 4px rgba(42, 94, 68, 0.2)'
    },
    disabled: {
      color: 'var(--primary-300)',
      opacity: 0.6
    }
  },

  // Success buttons
  success: {
    base: {
      backgroundColor: 'var(--success-500)', /* Mid Green */
      color: 'var(--success-foreground)', /* Cream */
      borderColor: 'var(--success-500)'
    },
    hover: {
      backgroundColor: 'var(--success-600)', /* Deep Green */
      borderColor: 'var(--success-600)'
    },
    active: {
      backgroundColor: 'var(--success-700)' /* Deep Green */
    },
    focus: {
      boxShadow: '0 0 0 2px var(--success-500), 0 0 0 4px rgba(99, 154, 115, 0.2)'
    },
    disabled: {
      backgroundColor: 'var(--success-200)', /* Light Mint Green */
      borderColor: 'var(--success-200)',
      opacity: 0.6
    }
  },

  // Warning buttons
  warning: {
    base: {
      backgroundColor: 'var(--warning-500)',
      color: 'white',
      borderColor: 'var(--warning-500)'
    },
    hover: {
      backgroundColor: 'var(--warning-600)',
      borderColor: 'var(--warning-600)'
    },
    active: {
      backgroundColor: 'var(--warning-700)'
    },
    focus: {
      boxShadow: '0 0 0 2px var(--warning-500), 0 0 0 4px rgba(245, 158, 11, 0.2)'
    },
    disabled: {
      backgroundColor: 'var(--warning-200)',
      borderColor: 'var(--warning-200)',
      opacity: 0.6
    }
  },

  // Error/destructive buttons
  destructive: {
    base: {
      backgroundColor: 'var(--error-500)',
      color: 'white',
      borderColor: 'var(--error-500)'
    },
    hover: {
      backgroundColor: 'var(--error-600)',
      borderColor: 'var(--error-600)'
    },
    active: {
      backgroundColor: 'var(--error-700)'
    },
    focus: {
      boxShadow: '0 0 0 2px var(--error-500), 0 0 0 4px rgba(239, 68, 68, 0.2)'
    },
    disabled: {
      backgroundColor: 'var(--error-200)',
      borderColor: 'var(--error-200)',
      opacity: 0.6
    }
  }
}

// 🃏 Card Background Variants
export const cardVariants = {
  // Default cream card
  default: {
    base: {
      backgroundColor: 'var(--background)', /* Cream */
      borderColor: 'var(--accent-200)', /* Light Mint Green */
      borderWidth: '1px',
      borderStyle: 'solid'
    },
    hover: {
      backgroundColor: 'var(--accent-50)', /* Pale Green Tint */
      borderColor: 'var(--accent-300)' /* Mid Green */
    },
    shadow: {
      base: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      hover: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)'
    }
  },

  // Subtle gradient cards
  gradient: {
    base: {
      background: 'linear-gradient(to bottom right, var(--background), var(--accent-50))',
      borderColor: 'var(--accent-100)',
      borderWidth: '1px',
      borderStyle: 'solid'
    },
    hover: {
      background: 'linear-gradient(to bottom right, var(--accent-50), var(--accent-100))',
      borderColor: 'var(--accent-200)'
    },
    shadow: {
      base: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      hover: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)'
    }
  },

  // Elevated cards
  elevated: {
    base: {
      backgroundColor: 'var(--background)', /* Cream */
      borderColor: 'var(--accent-200)',
      borderWidth: '1px',
      borderStyle: 'solid'
    },
    hover: {
      backgroundColor: 'var(--accent-50)' /* Pale Green Tint */
    },
    shadow: {
      base: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
      hover: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)'
    }
  },

  // Colored cards
  colored: {
    primary: {
      base: {
        backgroundColor: 'var(--accent-50)', /* Pale Green Tint */
        borderColor: 'var(--accent-200)',
        color: 'var(--primary-900)' /* Deep Forest Green */
      },
      hover: {
        backgroundColor: 'var(--accent-100)', /* Light Mint Green */
        borderColor: 'var(--accent-300)'
      }
    },
    secondary: {
      base: {
        backgroundColor: 'var(--accent-100)', /* Light Mint Green */
        borderColor: 'var(--accent-200)',
        color: 'var(--primary-900)' /* Deep Forest Green */
      },
      hover: {
        backgroundColor: 'var(--accent-200)',
        borderColor: 'var(--accent-300)'
      }
    },
    accent: {
      base: {
        backgroundColor: 'var(--accent-200)', /* Light Mint Green */
        borderColor: 'var(--accent-300)',
        color: 'var(--primary-900)' /* Deep Forest Green */
      },
      hover: {
        backgroundColor: 'var(--accent-300)', /* Mid Green */
        borderColor: 'var(--accent-400)'
      }
    }
  }
}

// 🎨 Focus Ring Styles
export const focusRings = {
  primary: {
    outline: 'none',
    boxShadow: '0 0 0 2px var(--primary-500), 0 0 0 4px rgba(42, 94, 68, 0.2)'
  },
  secondary: {
    outline: 'none',
    boxShadow: '0 0 0 2px var(--secondary-500), 0 0 0 4px rgba(99, 154, 115, 0.2)'
  },
  success: {
    outline: 'none',
    boxShadow: '0 0 0 2px var(--success-500), 0 0 0 4px rgba(99, 154, 115, 0.2)'
  },
  warning: {
    outline: 'none',
    boxShadow: '0 0 0 2px var(--warning-500), 0 0 0 4px rgba(245, 158, 11, 0.2)'
  },
  error: {
    outline: 'none',
    boxShadow: '0 0 0 2px var(--error-500), 0 0 0 4px rgba(239, 68, 68, 0.2)'
  },
  neutral: {
    outline: 'none',
    boxShadow: '0 0 0 2px var(--neutral-500), 0 0 0 4px rgba(33, 77, 58, 0.2)'
  }
}

// 🌈 Utility Functions
export const colorUtils = {
  // Get button styles as CSS properties object
  getButtonStyles: (variant: keyof typeof buttonVariants, state: 'base' | 'hover' | 'active' | 'focus' | 'disabled' = 'base') => {
    const v = buttonVariants[variant]
    return v[state] || v.base
  },

  // Get card styles as CSS properties object
  getCardStyles: (variant: keyof typeof cardVariants, colored?: keyof typeof cardVariants.colored, state: 'base' | 'hover' = 'base') => {
    if (variant === 'colored' && colored) {
      const c = cardVariants.colored[colored]
      return c[state] || c.base
    }
    const v = cardVariants[variant as keyof Omit<typeof cardVariants, 'colored'>]
    return v[state] || v.base
  },
  
  // Get section background by index
  getSectionBackground: (index: number, pattern: keyof typeof sectionPatterns = 'standard') => {
    const patterns = sectionPatterns[pattern]
    return patterns[index % patterns.length]
  },
  
  // Get gradient by use case
  getGradient: (useCase: keyof typeof gradientSystem, variant: string = 'primary') => {
    const gradients = gradientSystem[useCase]
    if (typeof gradients === 'object' && gradients !== null) {
      const gradientsObj = gradients as Record<string, string | Record<string, string>>
      const result = gradientsObj[variant] || gradientsObj.primary || Object.values(gradients)[0]
      return typeof result === 'string' ? result : Object.values(result)[0] || ''
    }
    return typeof gradients === 'string' ? gradients : ''
  }
}

// 📋 Color Accessibility Guidelines
export const accessibilityGuidelines = {
  // Minimum contrast ratios (WCAG 2.1)
  contrast: {
    normal: 4.5, // AA standard
    large: 3,    // AA standard for large text
    enhanced: 7  // AAA standard
  },
  
  // Recommended color combinations
  combinations: {
    highContrast: [
      { bg: 'white', text: 'slate-900' },
      { bg: 'emerald-600', text: 'white' },
      { bg: 'slate-900', text: 'white' }
    ],
    mediumContrast: [
      { bg: 'emerald-50', text: 'emerald-900' },
      { bg: 'teal-50', text: 'teal-900' },
      { bg: 'slate-100', text: 'slate-800' }
    ]
  }
}

// Export everything for easy access
export { colorTheme, gradientSystem, sectionPatterns }
