@import "tailwindcss";

:root {
  /* 🌱 HSSL Natural Green Color System */
  /* Modern, clean, and cohesive palette centered around sustainability and wellness */

  /* Base Colors */
  --background: #f4efe1; /* Cream - Site-wide background */
  --foreground: #214d3a; /* Deep Forest Green - Primary text */

  /* Primary Brand Colors - Deep Green (Accent Layer 1) */
  --primary: #2a5e44; /* Deep Green - Primary actions */
  --primary-foreground: #f4efe1; /* Cream - Text on primary */
  --primary-50: #d1e0cd; /* Pale Green Tint */
  --primary-100: #aecea9; /* Light Mint Green */
  --primary-200: #aecea9; /* Light Mint Green */
  --primary-300: #639a73; /* Mid Green */
  --primary-400: #639a73; /* Mid Green */
  --primary-500: #2a5e44; /* Deep Green */
  --primary-600: #2a5e44; /* Deep Green */
  --primary-700: #214d3a; /* Deep Forest Green */
  --primary-800: #214d3a; /* Deep Forest Green */
  --primary-900: #214d3a; /* Deep Forest Green */

  /* Secondary Brand Colors - Mid Green (Accent Layer 2) */
  --secondary: #639a73; /* Mid Green - Secondary buttons, highlights */
  --secondary-foreground: #f4efe1; /* Cream */
  --secondary-50: #d1e0cd; /* Pale Green Tint */
  --secondary-100: #aecea9; /* Light Mint Green */
  --secondary-200: #aecea9; /* Light Mint Green */
  --secondary-300: #639a73; /* Mid Green */
  --secondary-400: #639a73; /* Mid Green */
  --secondary-500: #639a73; /* Mid Green */
  --secondary-600: #639a73; /* Mid Green */
  --secondary-700: #2a5e44; /* Deep Green */
  --secondary-800: #214d3a; /* Deep Forest Green */
  --secondary-900: #214d3a; /* Deep Forest Green */

  /* Success Colors - Light Mint Green */
  --success: #639a73; /* Mid Green */
  --success-50: #d1e0cd; /* Pale Green Tint */
  --success-100: #aecea9; /* Light Mint Green */
  --success-200: #aecea9; /* Light Mint Green */
  --success-300: #639a73; /* Mid Green */
  --success-400: #639a73; /* Mid Green */
  --success-500: #639a73; /* Mid Green */
  --success-600: #2a5e44; /* Deep Green */
  --success-700: #2a5e44; /* Deep Green */
  --success-800: #214d3a; /* Deep Forest Green */
  --success-900: #214d3a; /* Deep Forest Green */

  /* Warning Colors - Earthy Amber */
  --warning: #f59e0b;
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  /* Error Colors - Muted Rust */
  --error: #ef4444;
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-200: #fecaca;
  --error-300: #fca5a5;
  --error-400: #f87171;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-800: #991b1b;
  --error-900: #7f1d1d;

  /* Accent Colors - Light Mint Green (Accent Layer 3) */
  --accent: #aecea9; /* Light Mint Green - Background layers, cards */
  --accent-foreground: #214d3a; /* Deep Forest Green */
  --accent-50: #d1e0cd; /* Pale Green Tint - Accent Layer 4 */
  --accent-100: #aecea9; /* Light Mint Green - Accent Layer 3 */
  --accent-200: #aecea9; /* Light Mint Green */
  --accent-300: #639a73; /* Mid Green */
  --accent-400: #639a73; /* Mid Green */
  --accent-500: #aecea9; /* Light Mint Green */
  --accent-600: #639a73; /* Mid Green */
  --accent-700: #2a5e44; /* Deep Green */
  --accent-800: #214d3a; /* Deep Forest Green */
  --accent-900: #214d3a; /* Deep Forest Green */

  /* Neutral Colors - Warm Greens and Creams */
  --neutral: #639a73; /* Mid Green */
  --neutral-50: #f4efe1; /* Cream */
  --neutral-100: #d1e0cd; /* Pale Green Tint */
  --neutral-200: #aecea9; /* Light Mint Green */
  --neutral-300: #639a73; /* Mid Green */
  --neutral-400: #2a5e44; /* Deep Green */
  --neutral-500: #214d3a; /* Deep Forest Green */
  --neutral-600: #214d3a; /* Deep Forest Green */
  --neutral-700: #214d3a; /* Deep Forest Green */
  --neutral-800: #214d3a; /* Deep Forest Green */
  --neutral-900: #214d3a; /* Deep Forest Green */

  /* Gray Scale - Cream and Green Tones */
  --gray-50: #f4efe1; /* Cream */
  --gray-100: #d1e0cd; /* Pale Green Tint */
  --gray-200: #aecea9; /* Light Mint Green */
  --gray-300: #639a73; /* Mid Green */
  --gray-400: #2a5e44; /* Deep Green */
  --gray-500: #214d3a; /* Deep Forest Green */
  --gray-600: #214d3a; /* Deep Forest Green */
  --gray-700: #214d3a; /* Deep Forest Green */
  --gray-800: #214d3a; /* Deep Forest Green */
  --gray-900: #214d3a; /* Deep Forest Green */

  /* Spacing Scale */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  --spacing-4xl: 6rem;     /* 96px */

  /* Typography Scale */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  --text-4xl: 2.25rem;     /* 36px */
  --text-5xl: 3rem;        /* 48px */
  --text-6xl: 3.75rem;     /* 60px */

  /* Border Radius */
  --radius-sm: 0.25rem;    /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-600);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-700);
}

/* 🎨 Enhanced Focus Styles */
.focus-ring {
  outline: none;
}

.focus-ring:focus {
  box-shadow: 0 0 0 2px var(--primary-500), 0 0 0 4px rgba(42, 94, 68, 0.2);
}

.focus-ring-secondary {
  outline: none;
}

.focus-ring-secondary:focus {
  box-shadow: 0 0 0 2px var(--secondary-500), 0 0 0 4px rgba(99, 154, 115, 0.2);
}

.focus-ring-success {
  outline: none;
}

.focus-ring-success:focus {
  box-shadow: 0 0 0 2px var(--success-500), 0 0 0 4px rgba(99, 154, 115, 0.2);
}

.focus-ring-warning {
  @apply focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2;
}

.focus-ring-error {
  @apply focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
}

/* 🌈 Gradient Utilities */
.gradient-hero-primary {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-500) 50%, var(--accent-500) 100%);
}

.gradient-hero-secondary {
  background: linear-gradient(135deg, var(--accent-200) 0%, var(--accent-100) 50%, var(--accent-50) 100%);
}

.gradient-card-subtle {
  background: linear-gradient(135deg, var(--accent-50) 0%, #ffffff 50%, var(--accent-100) 100%);
}

.gradient-interactive-primary {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-500) 100%);
}

.gradient-interactive-hover {
  background: linear-gradient(135deg, var(--primary-700) 0%, var(--secondary-600) 100%);
}

/* 🃏 Card Component Styles */
.card-default {
  background-color: var(--background); /* Cream */
  border: 1px solid var(--accent-200); /* Light Mint Green */
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.card-default:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
}

.card-gradient {
  background: linear-gradient(to bottom right, var(--background), var(--accent-50));
  border: 1px solid var(--accent-100);
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.card-gradient:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
}

.card-elevated {
  background-color: var(--background); /* Cream */
  border: 1px solid var(--accent-200);
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.card-elevated:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

.card-primary {
  background-color: var(--accent-50); /* Pale Green Tint */
  border: 1px solid var(--accent-200);
  color: var(--primary-900); /* Deep Forest Green */
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.card-primary:hover {
  background-color: var(--accent-100); /* Light Mint Green */
  border-color: var(--accent-300);
}

.card-secondary {
  background-color: var(--accent-100); /* Light Mint Green */
  border: 1px solid var(--accent-200);
  color: var(--primary-900); /* Deep Forest Green */
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.card-secondary:hover {
  background-color: var(--accent-200);
  border-color: var(--accent-300);
}

/* 🔘 Button Component Styles */
.btn-primary {
  background-color: var(--primary-600); /* Deep Green */
  color: var(--primary-foreground); /* Cream */
  border-color: var(--primary-600);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--primary-700); /* Deep Forest Green */
  border-color: var(--primary-700);
}

.btn-primary:active {
  background-color: var(--primary-800); /* Deep Forest Green */
}

.btn-primary:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-500), 0 0 0 4px rgba(42, 94, 68, 0.2);
}

.btn-primary:disabled {
  background-color: var(--primary-200); /* Light Mint Green */
  border-color: var(--primary-200);
  opacity: 0.6;
}

.btn-secondary {
  background-color: var(--accent-100); /* Light Mint Green */
  color: var(--primary-700); /* Deep Forest Green */
  border-color: var(--accent-200);
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--accent-200); /* Light Mint Green */
  border-color: var(--accent-300);
}

.btn-secondary:active {
  background-color: var(--accent-300); /* Mid Green */
}

.btn-secondary:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--accent-500), 0 0 0 4px rgba(174, 206, 169, 0.2);
}

.btn-secondary:disabled {
  background-color: var(--accent-50); /* Pale Green Tint */
  color: var(--accent-400);
  opacity: 0.6;
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-600); /* Deep Green */
  border-color: var(--primary-600);
  transition: all 0.2s ease;
}

.btn-outline:hover {
  background-color: var(--accent-50); /* Pale Green Tint */
  border-color: var(--primary-700);
  color: var(--primary-700); /* Deep Forest Green */
}

.btn-outline:active {
  background-color: var(--accent-100); /* Light Mint Green */
}

.btn-outline:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-500), 0 0 0 4px rgba(42, 94, 68, 0.2);
}

.btn-outline:disabled {
  color: var(--primary-300);
  border-color: var(--primary-300);
  opacity: 0.6;
}

.btn-ghost {
  background-color: transparent;
  color: var(--primary-600); /* Deep Green */
  border-color: transparent;
  transition: all 0.2s ease;
}

.btn-ghost:hover {
  background-color: var(--accent-50); /* Pale Green Tint */
  color: var(--primary-700); /* Deep Forest Green */
}

.btn-ghost:active {
  background-color: var(--accent-100); /* Light Mint Green */
}

.btn-ghost:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-500), 0 0 0 4px rgba(42, 94, 68, 0.2);
}

.btn-ghost:disabled {
  color: var(--primary-300);
  opacity: 0.6;
}

.btn-success {
  background-color: var(--success-500); /* Mid Green */
  color: var(--success-foreground); /* Cream */
  border-color: var(--success-500);
  transition: all 0.2s ease;
}

.btn-success:hover {
  background-color: var(--success-600); /* Deep Green */
  border-color: var(--success-600);
}

.btn-success:active {
  background-color: var(--success-700); /* Deep Green */
}

.btn-success:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--success-500), 0 0 0 4px rgba(99, 154, 115, 0.2);
}

.btn-success:disabled {
  background-color: var(--success-200); /* Light Mint Green */
  border-color: var(--success-200);
  opacity: 0.6;
}

.btn-destructive {
  background-color: var(--error-500);
  color: white;
  border-color: var(--error-500);
  transition: all 0.2s ease;
}

.btn-destructive:hover {
  background-color: var(--error-600);
  border-color: var(--error-600);
}

.btn-destructive:active {
  background-color: var(--error-700);
}

.btn-destructive:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--error-500), 0 0 0 4px rgba(239, 68, 68, 0.2);
}

.btn-destructive:disabled {
  background-color: var(--error-200);
  border-color: var(--error-200);
  opacity: 0.6;
}

/* 📱 Responsive Design Utilities */
.section-padding {
  @apply py-16 lg:py-24;
}

.section-padding-lg {
  @apply py-24 lg:py-32;
}

.container-responsive {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

/* Optimized animations using transform and opacity only */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Hardware acceleration for smooth animations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

/* Smooth transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Focus styles for accessibility */
.focus-visible:focus {
  outline: 2px solid var(--primary-600); /* Deep Green */
  outline-offset: 2px;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
