# 🌱 HSSL Natural Green Color System Guide

## Overview
This comprehensive color system establishes a modern, clean, and cohesive visual identity for the HSSL website, centered around sustainability, education, and wellness. The aesthetic is natural, calming, and minimal — evoking trust, clarity, and eco-conscious values.

## 🎨 Natural Green Color Palette

### Background
- **Name:** Cream
- **RGB:** rgb(244,239,225)
- **Hex:** #f4efe1
- **Use:** Site-wide background for a soft, warm, welcoming feel

### Primary Text
- **Name:** Deep Forest Green
- **RGB:** rgb(33,77,58)
- **Hex:** #214d3a
- **Use:** Body text and headings — deep, legible, and grounded

### Accent Layer 1 - Deep Green
- **Name:** Deep Green
- **RGB:** rgb(42,94,68)
- **Hex:** #2a5e44
- **Use:** Primary action buttons, key UI elements, and branding

### Accent Layer 2 - Mid Green
- **Name:** Mid Green
- **RGB:** rgb(99,154,115)
- **Hex:** #639a73
- **Use:** Secondary buttons, highlights, and visual balance

### Accent Layer 3 - Light Mint Green
- **Name:** Light Mint Green
- **RGB:** rgb(174,206,169)
- **Hex:** #aecea9
- **Use:** Background layers, cards, or soft dividers

### Accent Layer 4 - Pale Green Tint
- **Name:** Pale Green Tint
- **RGB:** rgb(209,224,205)
- **Hex:** #d1e0cd
- **Use:** Hover states, input backgrounds, or neutral zones

## 🎯 Color Usage Guidelines

### Primary Actions (Deep Green - Accent Layer 1)
**Use for:** Main CTAs, primary buttons, key interactive elements
```css
/* CSS Variables */
var(--primary-600)  /* Deep Green */
var(--primary-700)  /* Deep Forest Green */

/* Tailwind Classes (using our natural green gray scale) */
bg-gray-400 text-gray-50    /* Deep Green background with Cream text */
hover:bg-gray-500
focus:ring-gray-400
border-gray-400
```

### Secondary Actions (Mid Green - Accent Layer 2)
**Use for:** Supporting buttons, secondary CTAs, info components
```css
/* CSS Variables */
var(--secondary-500)  /* Mid Green */
var(--secondary-600)  /* Mid Green */

/* Tailwind Classes (using our natural green gray scale) */
bg-gray-300 text-gray-50    /* Mid Green background with Cream text */
hover:bg-gray-400
focus:ring-gray-300
border-gray-300

/* CSS Variables */
var(--secondary-500)
var(--secondary-600)
```

### Background Layers (Light Mint Green - Accent Layer 3)
**Use for:** Card backgrounds, soft dividers, content sections
```css
/* CSS Variables */
var(--accent-500)   /* Light Mint Green */
var(--accent-100)   /* Light Mint Green */

/* Tailwind Classes (using our natural green gray scale) */
bg-gray-200 text-gray-900
border-gray-200
```

### Hover States (Pale Green Tint - Accent Layer 4)
**Use for:** Hover states, input backgrounds, neutral zones
```css
/* CSS Variables */
var(--accent-50)    /* Pale Green Tint */

/* Tailwind Classes (using our natural green gray scale) */
bg-gray-100 text-gray-900
hover:bg-gray-100
border-gray-100
```

### Success States (Natural Green)
**Use for:** Success messages, positive feedback, completed states
```css
/* CSS Variables */
var(--success-500)  /* Mid Green */
var(--success-50)   /* Pale Green Tint */

/* Tailwind Classes (using our natural green gray scale) */
bg-gray-300 text-gray-50    /* Mid Green background with Cream text */
bg-gray-100 text-gray-500   /* Pale Green Tint background with Deep Forest Green text */
border-gray-200
```

### Warning States (Earthy Amber)
**Use for:** Warnings, caution messages, pending states
```css
/* CSS Variables */
var(--warning-500)
var(--warning-50)

/* Tailwind Classes */
bg-amber-500 text-white
bg-amber-50 text-amber-700
border-amber-200
```

### Error States (Muted Red)
**Use for:** Error messages, destructive actions, failed states
```css
/* CSS Variables */
var(--error-500)
var(--error-50)

/* Tailwind Classes */
bg-red-500 text-white
bg-red-50 text-red-700
border-red-200
```

### Neutral/Background (Cream & Natural Green Tones)
**Use for:** Site backgrounds, borders, muted text, disabled states
```css
/* CSS Variables */
var(--background)   /* Cream - Site-wide background */
var(--neutral-100)  /* Pale Green Tint */
var(--neutral-600)  /* Deep Forest Green */

/* Tailwind Classes (using our natural green gray scale) */
bg-gray-50 text-gray-500    /* Cream background with Deep Forest Green text */
bg-gray-100 text-gray-600   /* Pale Green Tint with Deep Forest Green text */
border-gray-200             /* Light Mint Green borders */
```

## 🌈 Natural Green Gradients

### Hero Sections
```css
/* Primary Hero - Deep Green to Mid Green */
bg-gradient-to-br from-gray-400 via-gray-300 to-gray-200

/* Secondary Hero - Light layers */
bg-gradient-to-br from-gray-200 via-gray-100 to-gray-50

/* CSS Variables */
linear-gradient(135deg, var(--primary-600), var(--secondary-500))
linear-gradient(135deg, var(--accent-200), var(--accent-100), var(--accent-50))
```

### Card Backgrounds
```css
/* Subtle Card - Cream to Light Mint */
bg-gradient-to-br from-gray-50 via-white to-gray-100

/* Medium Card - Natural green layers */
bg-gradient-to-br from-gray-100 via-gray-200 to-gray-100

/* CSS Variables */
linear-gradient(135deg, var(--accent-50), #ffffff, var(--accent-100))
linear-gradient(135deg, var(--neutral-50), var(--neutral-100))
```

## 🧭 Design Goals

The natural green color system is designed to:

- **Convey calmness, cleanliness, and nature** through soft, organic tones
- **Ensure strong text-background contrast** (WCAG compliant) for accessibility
- **Use rounded corners and soft shadows** for a modern, approachable feel
- **Maintain high readability and accessibility** across all color combinations
- **Avoid harsh or saturated colors** — let natural greens dominate the tone
- **Evoke trust, clarity, and eco-conscious values** through thoughtful color choices

### Visual Hierarchy
1. **Deep Forest Green (#214d3a)** - Primary text and headings
2. **Deep Green (#2a5e44)** - Primary actions and key UI elements
3. **Mid Green (#639a73)** - Secondary actions and highlights
4. **Light Mint Green (#aecea9)** - Background layers and cards
5. **Pale Green Tint (#d1e0cd)** - Hover states and neutral zones
6. **Cream (#f4efe1)** - Site-wide background for warmth and comfort

### Interactive Elements
```css
/* Primary Button - Natural Green Gradient */
bg-gradient-to-r from-gray-400 to-gray-300
hover:from-gray-500 hover:to-gray-400

/* Secondary Button - Light Natural Green */
bg-gradient-to-r from-gray-300 to-gray-200
hover:from-gray-400 hover:to-gray-300
```

## 🧩 Component Guidelines

### Buttons
```tsx
// Primary Action
<Button className="btn-primary">Primary Action</Button>

// Secondary Action
<Button className="btn-secondary">Secondary Action</Button>

// Outline Style
<Button className="btn-outline">Outline Button</Button>

// Ghost Style
<Button className="btn-ghost">Ghost Button</Button>
```

### Cards
```tsx
// Default Card
<Card className="card-default">Content</Card>

// Gradient Card
<Card className="card-gradient">Content</Card>

// Colored Card
<Card className="card-primary">Natural Green Content</Card>
<Card className="card-secondary">Light Green Content</Card>
```

### Focus States
```tsx
// Primary Focus
<input className="focus-ring" />

// Secondary Focus
<input className="focus-ring-secondary" />

// Success Focus
<input className="focus-ring-success" />
```

## 📋 Section Background Patterns

### Standard Alternating
```css
/* Section 1 */ bg-white
/* Section 2 */ bg-gradient-to-br from-emerald-50 via-white to-teal-50
/* Section 3 */ bg-emerald-50
/* Section 4 */ bg-gradient-to-br from-teal-50 via-emerald-50 to-green-50
```

### Subtle Gradient
```css
/* Section 1 */ bg-gradient-to-br from-white via-emerald-50 to-white
/* Section 2 */ bg-gradient-to-br from-emerald-50 via-white to-teal-50
/* Section 3 */ bg-gradient-to-br from-teal-50 via-emerald-50 to-white
/* Section 4 */ bg-gradient-to-br from-white via-teal-50 to-emerald-50
```

## ♿ Accessibility Guidelines

### Contrast Ratios (WCAG 2.1)
- **Normal Text:** 4.5:1 minimum (AA)
- **Large Text:** 3:1 minimum (AA)
- **Enhanced:** 7:1 (AAA)

### High Contrast Combinations
```css
/* Cream background with Deep Forest Green text */
bg-gray-50 text-gray-500

/* Deep Green background with Cream text */
bg-gray-400 text-gray-50

/* Deep Forest Green background with Cream text */
bg-gray-500 text-gray-50
```

### Medium Contrast Combinations
```css
/* Pale Green Tint with Deep Forest Green text */
bg-gray-100 text-gray-500

/* Light Mint Green with Deep Forest Green text */
bg-gray-200 text-gray-500

/* Mid Green with Cream text */
bg-gray-300 text-gray-50
```

## 🚀 Implementation Examples

### Using the Color Utils
```tsx
import { colorUtils } from '@/lib/colors'

// Get complete button classes
const buttonClasses = colorUtils.getButtonClasses('primary')

// Get complete card classes
const cardClasses = colorUtils.getCardClasses('gradient')

// Get section background by index
const sectionBg = colorUtils.getSectionBackground(2, 'subtle')

// Get gradient by use case
const heroGradient = colorUtils.getGradient('hero', 'primary')
```

### Using Color Theme
```tsx
import { colorTheme } from '@/lib/animations'

// Primary gradient
className={`bg-gradient-to-r ${colorTheme.primary.gradient}`}

// Hover states
className={`${colorTheme.primary.bg} ${colorTheme.primary.bgHover}`}

// Focus rings
className={`${colorTheme.primary.ring}`}
```

## 📝 Best Practices

1. **Consistency:** Always use the defined color roles for their intended purposes
2. **Accessibility:** Test color combinations for sufficient contrast
3. **Hierarchy:** Use color to establish clear visual hierarchy
4. **Restraint:** Don't use too many colors in a single component
5. **Context:** Consider the emotional context of color choices
6. **Testing:** Test colors across different devices and lighting conditions

## 🔧 Maintenance

- All colors are centralized in `/src/lib/colors.ts` and `/src/lib/animations.ts`
- CSS variables are defined in `/src/app/globals.css`
- Update colors in one place to affect the entire system
- Use the utility functions for consistent application
